export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    SIGNUP: '/api/v1/auth/register',
    LOGOUT: '/api/v1/auth/logout',
    REFRESH: '/api/v1/auth/refresh',
    ME: '/api/v1/auth/me',
  },
  USERS: {
    LIST: '/api/v1/users',
    CREATE: '/api/v1/users',
    GET: (id: string) => `/api/v1/users/${id}`,
    UPDATE: (id: string) => `/api/v1/users/${id}`,
    DELETE: (id: string) => `/api/v1/users/${id}`,
  },
  RESTAURANTS: {
    LIST: '/api/v1/restaurants',
    CREATE: '/api/v1/restaurants',
    GET: (id: string) => `/api/v1/restaurants/${id}`,
    UPDATE: (id: string) => `/api/v1/restaurants/${id}`,
    DELETE: (id: string) => `/api/v1/restaurants/${id}`,
  },
  AUDIT_LOGS: {
    LIST: '/api/v1/audit-logs',
    CREATE: '/api/v1/audit-logs',
    BULK_CREATE: '/api/v1/audit-logs/bulk',
    GET: (id: string) => `/api/v1/audit-logs/${id}`,
    DELETE: (id: string) => `/api/v1/audit-logs/${id}`,
    RESTORE: (id: string) => `/api/v1/audit-logs/${id}/restore`,
    STATS: '/api/v1/audit-logs/stats',
    FILTER_OPTIONS: '/api/v1/audit-logs/filter-options',
    USER_LOGS: (userId: number) => `/api/v1/audit-logs/user/${userId}`,
    RESOURCE_LOGS: (resourceType: string, resourceId: string) =>
      `/api/v1/audit-logs/resource/${resourceType}/${resourceId}`,
  },
  SALES: {
    LIST: '/api/v1/sales',
    CREATE: '/api/v1/sales',
    GET: (id: string) => `/api/v1/sales/${id}`,
    UPDATE: (id: string) => `/api/v1/sales/${id}`,
    DELETE: (id: string) => `/api/v1/sales/${id}`,
    REPORTS: '/api/v1/sales/reports',
  },
  DASHBOARD: {
    ADMIN: '/api/v1/dashboard/admin',
    USER: '/api/v1/dashboard/user',
    OVERVIEW: '/api/v1/dashboard/overview',
    METRICS: '/api/v1/dashboard/metrics',
    ACTIVITY: '/api/v1/dashboard/activity',
    SALES_TREND: '/api/v1/dashboard/sales-trend',
  },
  ANALYSIS: {
    DASHBOARD: '/api/v1/analysis/dashboard',
    BRANCH: '/api/v1/analysis/branch',
    KPI: '/api/v1/analysis/kpi',
  },
  WHATSAPP: {
    STATUS: '/api/v1/whatsapp/status',
    QR: '/api/v1/whatsapp/qr',
    PAIR: '/api/v1/whatsapp/pair',
    DISCONNECT: '/api/v1/whatsapp/disconnect',
  },
} as const;

export const USER_ROLES = {
  ADMIN: 'admin',
  STAFF: 'staff',
  USER: 'user',
} as const;

export const QUERY_KEYS = {
  AUTH: ['auth'],
  USERS: ['users'],
  RESTAURANTS: ['restaurants'],
  SALES: ['sales'],
  DASHBOARD: ['dashboard'],
  PROFILE: ['profile'],
  SETTINGS: ['settings'],
} as const;

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'broku_auth_token',
  USER_PREFERENCES: 'broku_user_preferences',
  THEME: 'broku_theme',
} as const;

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
} as const;

export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  DISPLAY_WITH_TIME: 'MMM dd, yyyy HH:mm',
  INPUT: 'yyyy-MM-dd',
  API: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
} as const;
