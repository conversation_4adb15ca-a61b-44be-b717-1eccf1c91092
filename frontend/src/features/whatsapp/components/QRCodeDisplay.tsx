import { useState } from 'react';
import { Button } from '@/shared/components/ui/button';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import { env } from '@/lib/config/env';

export const QRCodeDisplay = () => {
  const [timestamp, setTimestamp] = useState(Date.now());

  const refreshQrCode = () => {
    setTimestamp(Date.now());
  };

  return (
    <div className="flex flex-col items-center">
      <img
        src={`${env.API_BASE_URL}${API_ENDPOINTS.WHATSAPP.QR}?_t=${timestamp}`}
        alt="WhatsApp QR Code"
        className="h-64 w-64"
      />
      <Button onClick={refreshQrCode} className="mt-4">
        Refresh QR Code
      </Button>
    </div>
  );
};
