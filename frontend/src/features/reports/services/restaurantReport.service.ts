import { apiClient } from '@/lib/api/client';
import type {
  RestaurantReportInput,
  RestaurantReport,
  RestaurantReportsResponse,
  RestaurantReportFilters,
} from '../types';

const BASE_URL = '/sales/reports';

export const restaurantReportService = {
  /**
   * Create a new restaurant report
   */
  async createReport(reportData: RestaurantReportInput): Promise<RestaurantReport> {
    const response: {
      success: boolean;
      message: string;
      data: RestaurantReport;
    } = await apiClient.post(BASE_URL, reportData);

    if (!response.success) {
      // Check if the message indicates success despite success flag being false
      const message = response.message || 'Failed to create restaurant report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      // If message indicates success, log the inconsistency but don't throw
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Get restaurant reports with optional filters
   */
  async getReports(filters: RestaurantReportFilters = {}): Promise<RestaurantReportsResponse> {
    const params = new URLSearchParams();

    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.restaurantName) params.append('restaurantName', filters.restaurantName);

    const response: {
      success: boolean;
      message: string;
      data: RestaurantReportsResponse;
    } = await apiClient.get(`${BASE_URL}?${params.toString()}`);

    if (!response.success) {
      const message = response.message || 'Failed to fetch restaurant reports';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Get a specific restaurant report by ID
   */
  async getReportById(id: number): Promise<RestaurantReport> {
    const response: {
      success: boolean;
      message: string;
      data: RestaurantReport;
    } = await apiClient.get(`${BASE_URL}/${id}`);

    if (!response.success) {
      const message = response.message || 'Failed to fetch restaurant report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Update an existing restaurant report
   */
  async updateReport(
    id: number,
    reportData: Partial<RestaurantReportInput>
  ): Promise<RestaurantReport> {
    const response: {
      success: boolean;
      message: string;
      data: RestaurantReport;
    } = await apiClient.put(`${BASE_URL}/${id}`, reportData);

    if (!response.success) {
      const message = response.message || 'Failed to update restaurant report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Delete a restaurant report
   */
  async deleteReport(id: number): Promise<void> {
    const response: {
      success: boolean;
      message: string;
    } = await apiClient.delete(`${BASE_URL}/${id}`);

    if (!response.success) {
      const message = response.message || 'Failed to delete restaurant report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }
  },
};
